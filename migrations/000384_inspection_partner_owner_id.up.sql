ALTER TABLE ams_asset_inspection_vehicle
ADD COLUMN IF NOT EXISTS partner_owner_id VARCHAR(40);

WITH cte AS (
    SELECT 
       aaiv.id,
       aa.partner_owner_id
    FROM
        ams_asset_inspection_vehicle aaiv
    JOIN ams_assets aa ON
        aa.id = aaiv.asset_vehicle_id
        AND aa.deleted_at IS NULL
        AND aa.partner_owner_id IS NOT NULL
        AND aa.partner_owner_name = aaiv.partner_owner_name
)
UPDATE
    ams_asset_inspection_vehicle
SET
    partner_owner_id = cte.partner_owner_id
FROM
    cte
WHERE
    cte.id = ams_asset_inspection_vehicle.id;


INSERT INTO "uis_ANALYTIC_TYPES"
(code, "label", description)
VALUES
    ('DASHBOARD_TYRE_DEALER_CHART', 'Tyre Inspection Dashboard Chart', '-')
ON CONFLICT (code) DO NOTHING;

INSERT
INTO
    "uis_ANALYTICS"
(code, "label", description, sequence, analytic_type_code)
VALUES
    ('VEHICLE_INSPECTIONS_INSPECTED_CUSTOMER_OVER_TIME', 'Vehicle Inspections & Inspected Customers Over Time', '-', 1, 'DASHBOARD_TYRE_DEALER_CHART'),
    ('REGISTERED_VS_EXTERNAL_VEHICLE_INSPECTIONS_THIS_MONTH', 'Registered vs External Vehicle Inspections (This Month)', '-', 2, 'DASHBOARD_TYRE_DEALER_CHART'),
    ('NUMBER_OF_VEHICLES_INSPECTED', 'Number of Vehicles Inspected', '-', 3, 'DASHBOARD_TYRE_DEALER_CHART'),
    ('VEHICLE_INSPECTIONS_ON_EXTERNAL_VEHICLES_OVER_TIME', 'Vehicle Inspections on External Vehicles Over Time', '-', 4, 'DASHBOARD_TYRE_DEALER_CHART'),
    ('NUMBER_OF_REGISTERED_CUSTOMERS_BY_INSPECTION_FREQUENCY', 'Number of Registered Customers by Inspection Frequency', '-', 5, 'DASHBOARD_TYRE_DEALER_CHART'),
    ('TOP_5_MOST_COMMON_TYRES_USED_BY_CUSTOMERS', 'Top 5 Most Common Tyres Used by Customers', '-', 6, 'DASHBOARD_TYRE_DEALER_CHART'),
    ('TOP_5_MOST_COMMON_VEHICLES_USED_BY_CUSTOMERS', 'Top 5 Most Common Vehicles Used by Customers', '-', 7, 'DASHBOARD_TYRE_DEALER_CHART')
ON CONFLICT (code) DO NOTHING;

