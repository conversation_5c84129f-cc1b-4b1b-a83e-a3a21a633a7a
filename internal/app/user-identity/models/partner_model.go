package models

import (
	"assetfindr/pkg/common/commonmodel"
	"database/sql"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type Partner struct {
	commonmodel.ModelV2
	Name            string         `json:"name"`
	ServiceProvided string         `json:"service_provided"`
	StatusCode      string         `json:"status_code"`
	PartnerTypeCode string         `json:"partner_type_code"`
	Address         string         `json:"address"`
	Floor           sql.NullString `gorm:"default:null" json:"floor"`
	Unit            sql.NullString `gorm:"default:null" json:"unit"`
	MapLat          float64        `json:"map_lat"`
	MapLong         float64        `json:"map_long"`
	PhoneNumber1    string         `gorm:"column:phone_number_1;not null" json:"phone_number_1"`
	PhoneNumber2    sql.NullString `gorm:"column:phone_number_2;default:null" json:"phone_number_2"`
	Email           string         `json:"email"`
	Notes           string         `json:"notes"`
	TaxIdentity     string         `json:"tax_identity"`
	PICName         string         `gorm:"column:pic_name" json:"pic_name"`
	AddressMapLink  string         `json:"address_map_link"`
	Contacts        []Contact      `gorm:"many2many:uis_partner_contacts;" json:"contact_list"`
}

func (p Partner) TableName() string {
	return "uis_partners"
}

func (p *Partner) BeforeCreate(db *gorm.DB) error {
	p.SetUUID("ptr")
	p.ModelV2.BeforeCreate(db)
	return nil
}

func (p *Partner) BeforeUpdate(db *gorm.DB) error {
	p.ModelV2.BeforeUpdate(db)
	return nil
}

type PartnerStatus struct {
	Code        string `gorm:"primaryKey"`
	Label       string
	Description string
}

func (ps PartnerStatus) TableName() string {
	return "uis_PARTNER_STATUSES"
}

type PartnerType struct {
	Code        string `gorm:"primaryKey" json:"code"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

func (ps PartnerType) TableName() string {
	return "uis_PARTNER_TYPES"
}

type PartnerWhere struct {
	ID              string
	IDs             []string
	ClientID        string
	ShowDeleted     bool
	StatusCode      string
	PartnerTypeCode string
}

type PartnerCondition struct {
	Where       PartnerWhere
	Columns     []string
	IsForUpdate bool //see later
}

type GetPartnerListParam struct {
	commonmodel.ListRequest
	Cond PartnerCondition
}

type InspectionLocationChart struct {
	Name string      `json:"name"`
	Code null.String `json:"code"`
	Lat  float64     `json:"location_lat"`
	Long float64     `json:"location_long"`
}
