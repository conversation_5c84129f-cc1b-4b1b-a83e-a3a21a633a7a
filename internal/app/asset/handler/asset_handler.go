package handler

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/usecase"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

type AssetHandler struct {
	AssetUseCase        *usecase.AssetUseCase
	AssetVehicleUseCase *usecase.AssetVehicleUseCase
	AttachmentUseCase   *storageUsecase.AttachmentUseCase
}

func NewAssetHandler(
	assetUseCase *usecase.AssetUseCase,
	attachmentUseCase *storageUsecase.AttachmentUseCase,
	assetVehicleUseCase *usecase.AssetVehicleUseCase,
) *AssetHandler {
	return &AssetHandler{
		AssetUseCase:        assetUseCase,
		AssetVehicleUseCase: assetVehicleUseCase,
		AttachmentUseCase:   attachmentUseCase,
	}
}

func (h *<PERSON><PERSON><PERSON>and<PERSON>) UpdateAssetManagement(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.CreateUpdateAsset
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.UpdateAssetManagement(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) GetAssetManagement(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	resp, err := h.AssetUseCase.GetAssetManagement(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)

}

func (h *AssetHandler) CreateAssetManagement(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateUpdateAsset
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.CreateAssetManagement(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) GetListAssetManagement(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.AssetManagementListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	log.Println(req.StatusCodes)
	resp, err := h.AssetUseCase.GetListAssetManagement(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) GetAssets(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.AssetListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		commonlogger.Errorf("Failed in validating list asset request ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	recipesResponse, err := h.AssetUseCase.GetAssets(ctx, req)
	if err != nil {
		commonlogger.Errorf("Failed in getting assets ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in getting asset"})
		return
	}

	c.JSON(http.StatusOK, recipesResponse)
}

func (h *AssetHandler) GetAssetsByAdmin(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.AssetListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		commonlogger.Errorf("Failed in validating list asset request ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	recipesResponse, err := h.AssetUseCase.GetAssetsByAdmin(ctx, req)
	if err != nil {
		commonlogger.Errorf("Failed in getting assets ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in getting asset"})
		return
	}

	c.JSON(http.StatusOK, recipesResponse)
}

func (h *AssetHandler) GetAssetsSelections(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetSelectionsReq{}
	err := c.BindQuery(&req)
	if err != nil {
		commonlogger.Errorf("Failed in validating list asset request ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.GetAssetsSelections(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) GetAssetBrands(c *gin.Context) {
	ctx := c.Request.Context()
	brandTags := c.QueryArray("brand_tags")
	brands, err := h.AssetUseCase.GetAssetBrands(ctx, brandTags)
	if err != nil {
		commonlogger.Errorf("Failed in getting assets ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in getting asset"})
		return
	}

	c.JSON(http.StatusOK, commonmodel.ListResponse{
		TotalRecords: len(brands),
		PageSize:     len(brands),
		PageNo:       1,
		Data:         brands,
	})
}

func (avh *AssetHandler) UpdateAssetStatus(c *gin.Context) {
	assetID := c.Param("asset_id")

	var req struct {
		Status string `json:"status"`
	}

	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx := c.Request.Context()

	err = avh.AssetUseCase.UpdateAssetStatus(ctx, assetID, req.Status)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Asset status successfully updated",
		ReferenceID: assetID,
	})
}

func (h *AssetHandler) GetAssetLogs(c *gin.Context) {
	ctx := c.Request.Context()
	listRequest, err := commonmodel.ValidateListRequest(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	assetID := c.Param("asset_id")
	recipesResponse, err := h.AssetUseCase.GetAssetLogList(ctx, assetID, *listRequest)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, recipesResponse)
}

func (h *AssetHandler) ConvertToOptimax(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.ConvertToOptimax
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.ConvertAssetGeneralToAssetOptimax(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}
	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ConvertFromOptimax(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.ConvertFromOptimax
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	resp, err := h.AssetUseCase.ConvertAssetOptimaxToAssetGeneral(ctx, req)
	if err != nil {
		commonlogger.Warnf("error ConvertAssetOptimaxToAssetGeneral, %v", err)
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) GetAssetCategories(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.AssetUseCase.GetAssetCategories(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) GetAssetSubCategories(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.AssetSubCategoryListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	resp, err := h.AssetUseCase.GetAssetSubCategories(ctx, req.CategoryCode)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) RfidIsExists(c *gin.Context) {
	ctx := c.Request.Context()
	req := &dtos.AssetRFIDReq{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.RfidIsExists(ctx, req.RFIDs)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) SerialNumberIsExist(c *gin.Context) {
	ctx := c.Request.Context()
	req := &dtos.AssetSerialNumberReq{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.SerialNumberIsExist(ctx, req.SerialNumber)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ReferenceNumberIsExist(c *gin.Context) {
	ctx := c.Request.Context()
	req := &dtos.AssetReferenceNumberReq{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.ReferenceNumberIsExist(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) GetAssetCount(c *gin.Context) {
	ctx := c.Request.Context()
	var req struct {
		Parameters []string `form:"parameters"`
	}

	err := c.BindQuery(&req)
	if err != nil {
		commonlogger.Errorf("Failed when get asset count request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.GetAssetCount(ctx, req.Parameters)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) EnableTyreOptimax(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.EnableDisableTyreOptimax
	err := c.BindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.EnableTyreOptimax(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartAssetStatus(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.AssetStatusChartRequest{}
	err := c.BindQuery(&req)
	if err != nil {
		commonlogger.Errorf("Failed when get chart asset status request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.ChartAssetStatus(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartAssetBrands(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartAssetBrands(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) DisableTyreOptimax(c *gin.Context) {
	ctx := c.Request.Context()

	var req dtos.EnableDisableTyreOptimax
	err := c.BindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetUseCase.DisableTyreOptimax(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartAssetCustomCategories(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartAssetCustomCategories(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartAssetCustomCategoriesSubcategories(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartAssetCustomCategoriesSubcategories(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartTotalAssets(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartTotalAssets(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// Tyre Chart Handler

func (h *AssetHandler) ChartTyreStatus(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartTyreStatus(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartTyreNumber(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartTyreNumber(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}
func (h *AssetHandler) ChartTyreTread(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartTyreTread(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartTyreSize(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartTyreSize(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartTyreBrand(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartTyreBrand(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartAssetExpiredComponent(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartAssetExpiredComponent(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartTop5CustomersByTyres(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.AssetUseCase.ChartTop5CustomersByTyres(ctx)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
