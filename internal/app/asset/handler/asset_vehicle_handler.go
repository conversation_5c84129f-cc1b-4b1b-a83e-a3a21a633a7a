package handler

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/usecase"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gocarina/gocsv"
)

type AssetVehicleHandler struct {
	AssetUseCase        *usecase.AssetUseCase
	AssetVehicleUseCase *usecase.AssetVehicleUseCase
	AttachmentUseCase   *storageUsecase.AttachmentUseCase
}

func NewAssetVehicleHandler(
	assetUseCase *usecase.AssetUseCase,
	attachmentUseCase *storageUsecase.AttachmentUseCase,
	assetVehicleUseCase *usecase.AssetVehicleUseCase,
) *AssetVehicleHandler {
	return &AssetVehicleHandler{
		AssetUseCase:        assetUseCase,
		AssetVehicleUseCase: assetVehicleUseCase,
		AttachmentUseCase:   attachmentUseCase,
	}
}

func (h *AssetVehicleHandler) GetAssetVehicles(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.AssetVehicleListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	recipesResponse, err := h.AssetVehicleUseCase.GetAssetVehicles(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, recipesResponse)
}

func (ah *AssetVehicleHandler) GetAssetVehicleByID(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("id")

	assetVehicle, err := ah.AssetVehicleUseCase.GetAssetVehicleDetail(ctx, assetID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Asset not found"})
		return
	}

	c.JSON(http.StatusOK, assetVehicle)
}

func (avh *AssetVehicleHandler) CreateAssetVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	var vehicleDTO dtos.AssetVehicleListReceiver
	err := c.ShouldBindJSON(&vehicleDTO)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	err = avh.AssetVehicleUseCase.ValidateCreateAssetVehicle(ctx, &vehicleDTO)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	vehicleDTO.ClientID = claim.GetLoggedInClientID()
	asset, err := avh.AssetVehicleUseCase.CreateAssetVehicle(ctx, &vehicleDTO)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	assetListDTO := dtos.AssetListVehicleItemResponse{
		ID:                    asset.ID,
		CreatedAt:             asset.CreatedAt,
		UpdatedAt:             asset.UpdatedAt,
		VehicleName:           vehicleDTO.VehicleName,
		BrandID:               vehicleDTO.BrandID,
		BrandName:             vehicleDTO.BrandName,
		ModelNumber:           vehicleDTO.ModelNumber,
		SerialNumber:          vehicleDTO.SerialNumber,
		CategoryCode:          vehicleDTO.CategoryCode,
		ProductionYear:        vehicleDTO.ProductionYear,
		StatusCode:            vehicleDTO.StatusCode,
		Status:                vehicleDTO.Status,
		RegistrationNumber:    vehicleDTO.RegistrationNumber,
		VehicleBodyType:       vehicleDTO.VehicleBodyType,
		NumberOfTyres:         vehicleDTO.NumberOfTyres,
		AssingedToUserID:      vehicleDTO.AssingedToUserID,
		AttachedToParentAsset: vehicleDTO.AttachedToParentAsset,
		VehicleKM:             vehicleDTO.VehicleKM,
		NumberOfSpareTyres:    vehicleDTO.NumberOfSpareTyres,
		UseFleetOptimax:       asset.UseFleetOptimax,
		UseTyreOptimax:        asset.UseTyreOptimax,
	}

	response := commonmodel.CreateResponse{
		Success:     true,
		ReferenceID: assetListDTO.ID,
		Data:        assetListDTO,
		Message:     "Asset vehicle created successfully",
	}

	c.JSON(http.StatusCreated, response)
}

func (avh *AssetVehicleHandler) UpdateAssetVehicle(c *gin.Context) {
	assetID := c.Param("asset_id")
	ctx := c.Request.Context()

	var vehicleDTO dtos.AssetVehicleListReceiver
	err := c.ShouldBindJSON(&vehicleDTO)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	assetListDTO, err := avh.AssetVehicleUseCase.UpdateAssetVehicle(ctx, assetID, claim.GetLoggedInClientID(), &vehicleDTO)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	response := commonmodel.UpdateResponse{
		Success:     true,
		ReferenceID: assetListDTO.ID,
		Data:        assetListDTO,
		Message:     "Asset vehicle updated successfully",
	}

	c.JSON(http.StatusOK, response)
}

func (h *AssetVehicleHandler) GetVehicles(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.VehicleListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()

	recipesResponse, err := h.AssetVehicleUseCase.GetVehicles(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, recipesResponse)
}

func (h *AssetVehicleHandler) GetVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	ID := c.Param("id")
	resp, err := h.AssetVehicleUseCase.GetVehicle(ctx, ID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetVehicleHandler) CreateVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateVehicleReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetVehicleUseCase.CreateVehicle(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssetVehicleHandler) DeleteVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	ID := c.Param("id")
	resp, err := h.AssetVehicleUseCase.DeleteVehicle(ctx, ID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetVehicleHandler) UpdateVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	ID := c.Param("id")

	var req dtos.UpdateVehicleReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetVehicleUseCase.UpdateVehicle(ctx, ID, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (ah *AssetVehicleHandler) UpdateAssetVehicleKm(c *gin.Context) {
	ctx := c.Request.Context()
	var req struct {
		VehicleKM int    `json:"vehicle_km"`
		AssetID   string `json:"asset_id"`
	}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := ah.AssetVehicleUseCase.UpdateAssetVehicleKm(ctx, req.AssetID, req.VehicleKM)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (ah *AssetVehicleHandler) UpdateAssetVehicleMeter(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.UpdateVehicleMeterReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := ah.AssetVehicleUseCase.UpdateAssetVehicleMeter(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetVehicleHandler) BulkUloadAssetVehicles(c *gin.Context) {
	ctx := c.Request.Context()
	var req commonmodel.BulkUploadReq
	err := c.Bind(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	fileName := req.FileHeader.Filename

	data, oriFileBytes, err := h.AssetVehicleUseCase.ParseAssetVehicleBulkUpload(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	err = h.AssetVehicleUseCase.ValidateAssetVehicleBulkUpload(ctx, data)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	resp, err := h.AssetVehicleUseCase.AssetVehicleBulkUpload(ctx, fileName, oriFileBytes, data)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssetVehicleHandler) GetVehicleCSV(c *gin.Context) {
	time := time.Now().Format("02012006")
	filename := fmt.Sprintf(`attachment; filename="vehicle_%s.csv"`, time)
	c.Writer.Header().Set("Content-Type", "text/csv")
	c.Writer.Header().Set("Content-Disposition", filename)
	ctx := c.Request.Context()
	resp, err := h.AssetVehicleUseCase.GetVehiclesCSV(ctx)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}
	err = gocsv.Marshal(&resp, c.Writer)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}
	c.Status(http.StatusOK)
}

func (h *AssetVehicleHandler) UpdateAssetVehicleAxleConfiguration(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	var req dtos.UpdateAssetVehicleAxleConfigurationReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetVehicleUseCase.UpdateAssetVehicleAxleConfiguration(ctx, assetID, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetVehicleHandler) PopulatePeriodicAssetVehicleStatsHistories(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.AssetVehicleUseCase.PopulatePeriodicAssetVehicleStatsHistories(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetVehicleHandler) GetLastMonthAssetVehicleStatsHistory(c *gin.Context) {
	ctx := c.Request.Context()

	assetID := c.Param("asset_id")
	resp, err := h.AssetVehicleUseCase.GetLastMonthAssetVehicleStatsHistory(ctx, assetID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetVehicleHandler) ChartTotalVehicles(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetVehicleUseCase.ChartTotalVehicles(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetVehicleHandler) ChartAssetVehicleVsDigispectVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetVehicleUseCase.ChartAssetVehicleVsDigispectVehicle(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetVehicleHandler) ChartTop5VehicleBrandsUsedByCustomers(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.AssetVehicleUseCase.ChartTop5VehicleBrandsUsedByCustomers(ctx)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetVehicleHandler) ChartTop5CustomersByVehicles(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.AssetVehicleUseCase.ChartTop5CustomersByVehicles(ctx)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
