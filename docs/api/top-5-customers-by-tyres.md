# Top 5 Customers by Number of Tyres API

## Overview
This API endpoint returns the top 5 customers ranked by the number of tyres they own. This is useful for creating dashboard charts showing which customers have the most tyres registered in the system.

## Endpoint Details

**URL:** `GET /v1/assets-vehicles/charts/top-5-customers-by-tyres`

**Authentication:** Bearer <PERSON>ken (required)

**Content-Type:** `application/json`

## Request

### Headers
```
Authorization: Bearer <your-token>
Content-Type: application/json
```

### Query Parameters
None required.

## Response

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Success",
  "reference_id": "",
  "data": [
    {
      "y": 20,
      "name": "PT Kargo Nusantara",
      "code": "ptr-abc123"
    },
    {
      "y": 15,
      "name": "PT Mega Trasindo",
      "code": "ptr-def456"
    },
    {
      "y": 10,
      "name": "PT Sumber Makmur Fleet",
      "code": "ptr-ghi789"
    },
    {
      "y": 8,
      "name": "PT Jaya Prima Hauling",
      "code": "ptr-jkl012"
    },
    {
      "y": 20,
      "name": "PT ABC Logistics",
      "code": "ptr-mno345"
    }
  ]
}
```

### Response Fields
- `success` (boolean): Indicates if the request was successful
- `message` (string): Response message
- `reference_id` (string): Reference ID (usually empty for chart data)
- `data` (array): Array of chart data objects
  - `y` (number): Number of tyres owned by the customer
  - `name` (string): Customer/partner name
  - `code` (string): Customer/partner ID

### Error Response (4xx/5xx)
```json
{
  "error": "Error message description"
}
```

## Business Logic

The API:
1. Filters tyres that have a valid `partner_owner_id` and `partner_owner_name`
2. Groups tyres by customer (partner_owner_id and partner_owner_name)
3. Counts the number of tyres per customer
4. Returns the top 5 customers with the most tyres
5. Orders results by tyre count in descending order

## Database Query
The endpoint executes a SQL query that:
- Joins `ams_asset_tyres` with `ams_assets`
- Filters by client_id and asset_category_code = 'TYRE'
- Excludes records with null or empty partner_owner_id/partner_owner_name
- Groups by partner_owner_id and partner_owner_name
- Orders by count descending
- Limits to top 5 results

## Usage Example

### cURL
```bash
curl -X GET \
  'https://api.assetfindr.com/v1/assets-vehicles/charts/top-5-customers-by-tyres' \
  -H 'Authorization: Bearer your-token-here' \
  -H 'Content-Type: application/json'
```

### JavaScript (Fetch)
```javascript
fetch('/v1/assets-vehicles/charts/top-5-customers-by-tyres', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-token-here',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  console.log('Top 5 customers by tyres:', data.data);
});
```

## Chart Integration

This API is designed to work with chart libraries. The response format is compatible with:
- Chart.js
- D3.js
- Recharts
- Any chart library that accepts data in `{name, y}` format

### Chart.js Example
```javascript
const chartData = {
  labels: data.data.map(item => item.name),
  datasets: [{
    label: 'Number of Tyres',
    data: data.data.map(item => item.y),
    backgroundColor: 'rgba(54, 162, 235, 0.6)'
  }]
};
```

## Notes
- Only tyres with valid customer/partner information are included
- Results are limited to the authenticated user's client scope
- The chart shows actual tyre counts, not percentages
- Customer names are taken from the `partner_owner_name` field in assets
- This endpoint complements the vehicles chart to provide a complete view of customer asset distribution

## Related Endpoints
- `GET /v1/assets-vehicles/charts/top-5-customers-by-vehicles` - Top 5 customers by vehicle count
- `GET /v1/assets/charts/installed-tyres/top-5-tyres-used-by-customers` - Top 5 tyre types used by customers
